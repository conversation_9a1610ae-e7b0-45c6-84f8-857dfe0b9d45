<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            padding: 20px;
            background: #f2f2f7;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>GoMyHire 透视分析 - 集成测试</h1>
    
    <div class="test-section">
        <h2>1. 基础组件加载测试</h2>
        <button class="test-button" onclick="testComponentLoading()">测试组件加载</button>
        <div id="componentTest" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 文件上传组件测试</h2>
        <button class="test-button" onclick="testFileUpload()">测试文件上传组件</button>
        <div id="fileUploadTest" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 字段选择器测试</h2>
        <button class="test-button" onclick="testFieldSelector()">测试字段选择器</button>
        <div id="fieldSelectorTest" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 配置表单集成测试</h2>
        <button class="test-button" onclick="testConfigFormIntegration()">测试配置表单集成</button>
        <div id="configFormTest" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 端到端流程测试</h2>
        <button class="test-button" onclick="testEndToEndFlow()">测试完整流程</button>
        <div id="endToEndTest" class="test-result"></div>
    </div>

    <!-- 引入所有必要的JavaScript文件 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-dropdown.js"></script>
    <script src="src/js/components/smartoffice-field-selector.js"></script>
    <script src="src/js/components/smartoffice-file-upload.js"></script>
    <script src="src/js/components/smartoffice-data-preview.js"></script>
    <script src="src/js/components/smartoffice-data-table.js"></script>
    <script src="src/js/components/smartoffice-config-form.js"></script>
    <script src="src/js/components/smartoffice-config-list.js"></script>

    <script>
        // 测试函数
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.textContent = message;
        }

        function testComponentLoading() {
            try {
                // 检查SmartOffice命名空间
                if (typeof SmartOffice === 'undefined') {
                    throw new Error('SmartOffice命名空间未加载');
                }

                // 检查核心组件
                const requiredComponents = [
                    'SmartOffice.Core.EventBus',
                    'SmartOffice.Core.Storage',
                    'SmartOffice.Core.Router',
                    'SmartOffice.Utils.DOM',
                    'SmartOffice.Utils.Helpers',
                    'SmartOffice.Data.CSVParser',
                    'SmartOffice.Data.DataValidator',
                    'SmartOffice.Components.FileUpload',
                    'SmartOffice.Components.FieldSelector',
                    'SmartOffice.Components.ConfigForm'
                ];

                const missing = [];
                requiredComponents.forEach(component => {
                    const parts = component.split('.');
                    let obj = window;
                    for (const part of parts) {
                        if (!obj[part]) {
                            missing.push(component);
                            break;
                        }
                        obj = obj[part];
                    }
                });

                if (missing.length > 0) {
                    throw new Error('缺失组件: ' + missing.join(', '));
                }

                logResult('componentTest', '✅ 所有组件加载成功', 'success');
            } catch (error) {
                logResult('componentTest', '❌ 组件加载失败: ' + error.message, 'error');
            }
        }

        function testFileUpload() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.id = 'testFileUploadContainer';
                document.body.appendChild(testContainer);

                // 创建文件上传组件
                const fileUpload = new SmartOffice.Components.FileUpload({
                    containerId: 'testFileUploadContainer',
                    acceptedTypes: ['csv'],
                    maxSize: 5 * 1024 * 1024,
                    onUpload: function(result, file) {
                        logResult('fileUploadTest', '✅ 文件上传组件工作正常', 'success');
                    },
                    onError: function(error) {
                        logResult('fileUploadTest', '⚠️ 文件上传错误: ' + error, 'error');
                    }
                });

                if (fileUpload.init && fileUpload.init()) {
                    logResult('fileUploadTest', '✅ 文件上传组件初始化成功', 'success');
                } else {
                    throw new Error('文件上传组件初始化失败');
                }

                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                }, 1000);

            } catch (error) {
                logResult('fileUploadTest', '❌ 文件上传组件测试失败: ' + error.message, 'error');
            }
        }

        function testFieldSelector() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.id = 'testFieldSelectorContainer';
                document.body.appendChild(testContainer);

                // 创建字段选择器组件
                const fieldSelector = new SmartOffice.Components.FieldSelector({
                    containerId: 'testFieldSelectorContainer',
                    fields: [
                        { name: 'name', label: '姓名', type: 'text' },
                        { name: 'age', label: '年龄', type: 'number' },
                        { name: 'city', label: '城市', type: 'category' }
                    ],
                    multiple: true,
                    onSelect: function(selectedFields) {
                        logResult('fieldSelectorTest', '✅ 字段选择器工作正常', 'success');
                    }
                });

                if (fieldSelector.init && fieldSelector.init()) {
                    logResult('fieldSelectorTest', '✅ 字段选择器初始化成功', 'success');
                } else {
                    throw new Error('字段选择器初始化失败');
                }

                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                }, 1000);

            } catch (error) {
                logResult('fieldSelectorTest', '❌ 字段选择器测试失败: ' + error.message, 'error');
            }
        }

        function testConfigFormIntegration() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.id = 'testConfigFormContainer';
                document.body.appendChild(testContainer);

                // 创建配置表单组件
                const configForm = new SmartOffice.Components.ConfigForm('testConfigFormContainer');

                if (configForm.init) {
                    configForm.init();
                    logResult('configFormTest', '✅ 配置表单集成测试成功', 'success');
                } else {
                    throw new Error('配置表单没有init方法');
                }

                // 检查新增的方法
                const requiredMethods = [
                    'handleFileUploadComplete',
                    'updateAvailableFields',
                    'showFieldSelector',
                    'handleFieldSelectionComplete',
                    'showDataPreview'
                ];

                const missingMethods = [];
                requiredMethods.forEach(method => {
                    if (typeof configForm[method] !== 'function') {
                        missingMethods.push(method);
                    }
                });

                if (missingMethods.length > 0) {
                    throw new Error('缺失方法: ' + missingMethods.join(', '));
                }

                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                }, 1000);

            } catch (error) {
                logResult('configFormTest', '❌ 配置表单集成测试失败: ' + error.message, 'error');
            }
        }

        function testEndToEndFlow() {
            try {
                // 模拟端到端流程测试
                logResult('endToEndTest', '🔄 开始端到端流程测试...', 'info');

                // 1. 检查CSV解析器
                const csvParser = new SmartOffice.Data.CSVParser();
                const testCSV = 'name,age,city\nJohn,25,Beijing\nJane,30,Shanghai';
                const parseResult = csvParser.parse(testCSV);

                if (!parseResult || !parseResult.data || parseResult.data.length !== 2) {
                    throw new Error('CSV解析测试失败');
                }

                // 2. 检查数据验证器
                const validator = new SmartOffice.Data.DataValidator();
                const validationResult = validator.validateData(parseResult.data, parseResult.headers);

                if (!validationResult || !validationResult.isValid) {
                    throw new Error('数据验证测试失败');
                }

                // 3. 检查透视表引擎
                const pivotEngine = new SmartOffice.Data.PivotEngine();
                const pivotConfig = {
                    rowFields: ['city'],
                    columnFields: [],
                    valueFields: ['age'],
                    aggregation: 'sum'
                };

                const pivotResult = pivotEngine.generatePivotTable(parseResult.data, pivotConfig);

                if (!pivotResult || !pivotResult.data) {
                    throw new Error('透视表引擎测试失败');
                }

                logResult('endToEndTest', '✅ 端到端流程测试成功！所有组件协同工作正常', 'success');

            } catch (error) {
                logResult('endToEndTest', '❌ 端到端流程测试失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testComponentLoading();
            }, 500);
        });
    </script>
</body>
</html>
