<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端到端测试 - GoMyHire透视分析</title>
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/ios-theme.css">
    <link rel="stylesheet" href="src/css/mobile.css">
    <link rel="stylesheet" href="src/css/components/config-list.css">
    <link rel="stylesheet" href="src/css/components/config-form.css">
    <link rel="stylesheet" href="src/css/components/config-form-integration.css">
    <link rel="stylesheet" href="src/css/components/dropdown.css">
    <link rel="stylesheet" href="src/css/components/data-table.css">
    <style>
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 16px;
            z-index: 10000;
            max-height: 80vh;
            overflow-y: auto;
        }
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
            font-size: 12px;
        }
        .test-log {
            font-size: 11px;
            margin: 4px 0;
            padding: 4px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <!-- 测试面板 -->
    <div class="test-panel">
        <h3>端到端测试面板</h3>
        <button class="test-button" onclick="runFullTest()">运行完整测试</button>
        <button class="test-button" onclick="simulateFileUpload()">模拟文件上传</button>
        <button class="test-button" onclick="testFieldSelection()">测试字段选择</button>
        <button class="test-button" onclick="testDataPreview()">测试数据预览</button>
        <button class="test-button" onclick="clearLogs()">清空日志</button>
        <div id="testLogs"></div>
    </div>

    <!-- iOS风格状态栏占位 -->
    <div class="status-bar-spacer"></div>
    
    <!-- 主应用容器 -->
    <div id="app" class="app-container">
        <!-- 导航栏 -->
        <header class="nav-bar">
            <div class="nav-content">
                <button type="button" class="nav-button nav-back" id="navBack" style="display: none;">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
                <h1 class="nav-title" id="navTitle">透视分析</h1>
                <button type="button" class="nav-button nav-add" id="navAdd">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
            </div>
        </header>
        
        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 配置列表页面 -->
            <div class="page" id="configListPage">
                <div class="page-content">
                    <!-- 空状态提示 -->
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                            </svg>
                        </div>
                        <h3 class="empty-title">还没有透视表配置</h3>
                        <p class="empty-description">点击右上角的 + 按钮创建您的第一个透视表配置</p>
                    </div>
                    
                    <!-- 配置列表容器 -->
                    <div class="config-list" id="configList">
                        <!-- 配置卡片将在这里动态生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 配置表单页面 -->
            <div class="page page-hidden" id="configFormPage">
                <div class="page-content">
                    <!-- 表单内容将由ConfigFormComponent动态生成 -->
                </div>
            </div>
        </main>
        
        <!-- iOS风格加载指示器 -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
            </div>
        </div>
        
        <!-- iOS风格提示框容器 -->
        <div class="toast-container" id="toastContainer"></div>
    </div>
    
    <!-- JavaScript文件 - 按依赖顺序加载 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-dropdown.js"></script>
    <script src="src/js/components/smartoffice-field-selector.js"></script>
    <script src="src/js/components/smartoffice-file-upload.js"></script>
    <script src="src/js/components/smartoffice-data-preview.js"></script>
    <script src="src/js/components/smartoffice-data-table.js"></script>
    <script src="src/js/components/smartoffice-config-form.js"></script>
    <script src="src/js/components/smartoffice-config-list.js"></script>
    <script src="src/js/core/smartoffice-app.js"></script>

    <script>
        // 测试工具函数
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('testLogs');
            const logElement = document.createElement('div');
            logElement.className = `test-log ${type}`;
            logElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logElement);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
        }

        // 模拟文件上传
        function simulateFileUpload() {
            log('开始模拟文件上传测试...', 'info');
            
            try {
                // 首先切换到配置表单页面
                if (SmartOffice && SmartOffice.Core && SmartOffice.Core.Router) {
                    SmartOffice.Core.Router.navigateTo('configForm');
                    log('已切换到配置表单页面', 'success');
                    
                    // 等待页面加载完成后测试文件上传
                    setTimeout(() => {
                        testFileUploadIntegration();
                    }, 1000);
                } else {
                    throw new Error('路由系统未正确加载');
                }
            } catch (error) {
                log('模拟文件上传失败: ' + error.message, 'error');
            }
        }

        function testFileUploadIntegration() {
            try {
                // 创建模拟CSV数据
                const csvData = `姓名,年龄,城市,部门,薪资
张三,28,北京,技术部,15000
李四,32,上海,销售部,12000
王五,25,广州,市场部,10000`;

                // 创建模拟文件对象
                const blob = new Blob([csvData], { type: 'text/csv' });
                const file = new File([blob], 'test-data.csv', { type: 'text/csv' });

                // 获取配置表单组件实例
                const configFormComponent = SmartOffice.Core.App.components.configForm;
                if (!configFormComponent) {
                    throw new Error('配置表单组件未找到');
                }

                // 解析CSV数据
                const csvParser = new SmartOffice.Data.CSVParser();
                const parseResult = csvParser.parse(csvData);
                
                if (!parseResult || !parseResult.data) {
                    throw new Error('CSV解析失败');
                }

                log('CSV解析成功，数据行数: ' + parseResult.data.length, 'success');

                // 模拟文件上传完成
                if (configFormComponent.handleFileUploadComplete) {
                    configFormComponent.handleFileUploadComplete(parseResult, file);
                    log('文件上传完成处理成功', 'success');
                } else {
                    throw new Error('handleFileUploadComplete方法未找到');
                }

                // 检查可选字段是否已更新
                if (configFormComponent.availableFields && configFormComponent.availableFields.length > 0) {
                    log('可选字段已更新，字段数量: ' + configFormComponent.availableFields.length, 'success');
                } else {
                    log('可选字段未正确更新', 'warning');
                }

            } catch (error) {
                log('文件上传集成测试失败: ' + error.message, 'error');
            }
        }

        function testFieldSelection() {
            log('开始测试字段选择功能...', 'info');
            
            try {
                const configFormComponent = SmartOffice.Core.App.components.configForm;
                if (!configFormComponent) {
                    throw new Error('配置表单组件未找到');
                }

                // 确保有可选字段
                if (!configFormComponent.availableFields || configFormComponent.availableFields.length === 0) {
                    log('没有可选字段，先运行文件上传测试', 'warning');
                    return;
                }

                // 测试字段选择
                if (configFormComponent.showFieldSelector) {
                    configFormComponent.showFieldSelector('rowFields');
                    log('字段选择器显示成功', 'success');
                } else {
                    throw new Error('showFieldSelector方法未找到');
                }

            } catch (error) {
                log('字段选择测试失败: ' + error.message, 'error');
            }
        }

        function testDataPreview() {
            log('开始测试数据预览功能...', 'info');
            
            try {
                const configFormComponent = SmartOffice.Core.App.components.configForm;
                if (!configFormComponent) {
                    throw new Error('配置表单组件未找到');
                }

                // 检查数据预览是否可见
                const previewSection = document.getElementById('dataPreviewSection');
                if (previewSection && previewSection.style.display !== 'none') {
                    log('数据预览区域已显示', 'success');
                    
                    // 测试切换功能
                    if (configFormComponent.toggleDataPreview) {
                        configFormComponent.toggleDataPreview();
                        log('数据预览切换功能正常', 'success');
                    }
                } else {
                    log('数据预览区域未显示，请先上传文件', 'warning');
                }

            } catch (error) {
                log('数据预览测试失败: ' + error.message, 'error');
            }
        }

        function runFullTest() {
            log('开始运行完整端到端测试...', 'info');
            clearLogs();
            
            // 按顺序执行测试
            setTimeout(() => simulateFileUpload(), 500);
            setTimeout(() => testFieldSelection(), 3000);
            setTimeout(() => testDataPreview(), 4000);
            setTimeout(() => {
                log('完整端到端测试完成！', 'success');
            }, 5000);
        }

        // 应用初始化
        document.addEventListener('DOMContentLoaded', function() {
            try {
                if (typeof SmartOffice === 'undefined') {
                    throw new Error('SmartOffice命名空间未正确加载');
                }
                
                SmartOffice.Core.App.init();
                log('应用初始化成功', 'success');
                
                // 自动运行基础测试
                setTimeout(() => {
                    log('测试面板已准备就绪，可以开始测试', 'info');
                }, 1000);
                
            } catch (error) {
                log('应用启动失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
